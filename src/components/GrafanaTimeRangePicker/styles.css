/* Grafana Time Range Picker Styles - Application Theme */
.grafana-time-picker {
  position: relative;
  display: inline-block;
}

.grafana-time-picker-button {
  background-color: #ffffff !important;
  border-color: #d9d9d9 !important;
  color: #000000 !important;
  font-size: 13px;
  font-weight: 400;
  transition: all 0.2s ease;
}

.grafana-time-picker-button:hover {
  background-color: #f5f5f5 !important;
  border-color: #1b524f !important;
}

.grafana-time-picker-button:focus {
  background-color: #f5f5f5 !important;
  border-color: #1b524f !important;
  box-shadow: 0 0 0 2px rgba(27, 82, 79, 0.2) !important;
}

.grafana-time-picker-dropdown {
  background-color: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  max-height: 400px;
}

.grafana-search-input {
  background-color: #ffffff !important;
  border-color: #d9d9d9 !important;
  color: #000000 !important;
}

.grafana-search-input::placeholder {
  color: #8c8c8c !important;
}

.grafana-search-input:hover {
  border-color: #1b524f !important;
}

.grafana-search-input:focus {
  border-color: #1b524f !important;
  box-shadow: 0 0 0 2px rgba(27, 82, 79, 0.2) !important;
}

.grafana-option-item:hover {
  background-color: #f5f5f5 !important;
  color: #1b524f !important;
}

.grafana-option-item.selected {
  background-color: #e6f7ff !important;
  color: #1b524f !important;
  border-left: 2px solid #1b524f !important;
}

/* Custom scrollbar for options list */
.grafana-time-picker-dropdown ::-webkit-scrollbar {
  width: 6px;
}

.grafana-time-picker-dropdown ::-webkit-scrollbar-track {
  background: #f5f5f5;
}

.grafana-time-picker-dropdown ::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.grafana-time-picker-dropdown ::-webkit-scrollbar-thumb:hover {
  background: #1b524f;
}

/* Override Ant Design Input styles for light theme */
.grafana-time-picker-dropdown .ant-input {
  background-color: #ffffff !important;
  border-color: #d9d9d9 !important;
  color: #000000 !important;
}

.grafana-time-picker-dropdown .ant-input:hover {
  border-color: #1b524f !important;
}

.grafana-time-picker-dropdown .ant-input:focus {
  border-color: #1b524f !important;
  box-shadow: 0 0 0 2px rgba(27, 82, 79, 0.2) !important;
}

.grafana-time-picker-dropdown .ant-input::placeholder {
  color: #8c8c8c !important;
}

/* Override Ant Design Button styles */
.grafana-time-picker-dropdown .ant-btn-primary {
  background-color: #1b524f !important;
  border-color: #1b524f !important;
  color: #ffffff !important;
  font-weight: 500;
}

.grafana-time-picker-dropdown .ant-btn-primary:hover {
  background-color: #164340 !important;
  border-color: #164340 !important;
}

.grafana-time-picker-dropdown .ant-btn-primary:focus {
  background-color: #164340 !important;
  border-color: #164340 !important;
  box-shadow: 0 0 0 2px rgba(27, 82, 79, 0.2) !important;
}

/* Override Ant Design Divider styles */
.grafana-time-picker-dropdown .ant-divider-horizontal {
  border-color: #d9d9d9 !important;
  margin: 16px 0 !important;
}

/* Date Range Picker styles */
.grafana-time-picker-dropdown .ant-picker {
  background-color: #ffffff !important;
  border-color: #d9d9d9 !important;
  color: #000000 !important;
}

.grafana-time-picker-dropdown .ant-picker:hover {
  border-color: #1b524f !important;
}

.grafana-time-picker-dropdown .ant-picker:focus {
  border-color: #1b524f !important;
  box-shadow: 0 0 0 2px rgba(27, 82, 79, 0.2) !important;
}

.grafana-time-picker-dropdown .ant-picker-input > input {
  color: #000000 !important;
}

.grafana-time-picker-dropdown .ant-picker-input > input::placeholder {
  color: #8c8c8c !important;
}

/* Date picker button styles */
.grafana-time-picker-dropdown .ant-btn {
  background-color: #ffffff !important;
  border-color: #d9d9d9 !important;
  color: #000000 !important;
}

.grafana-time-picker-dropdown .ant-btn:hover {
  background-color: #f5f5f5 !important;
  border-color: #1b524f !important;
  color: #1b524f !important;
}

/* Enhanced DatePicker month navigation styles */
.grafana-time-picker-dropdown .ant-picker-header {
  padding: 8px 12px !important;
  border-bottom: 1px solid #f0f0f0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  height: 40px !important;
}

.grafana-time-picker-dropdown .ant-picker-header-view {
  font-weight: 600 !important;
  font-size: 14px !important;
  color: #1b524f !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex: 1 !important;
  height: 100% !important;
  gap: 8px !important;
}

.grafana-time-picker-dropdown .ant-picker-prev-icon,
.grafana-time-picker-dropdown .ant-picker-next-icon,
.grafana-time-picker-dropdown .ant-picker-super-prev-icon,
.grafana-time-picker-dropdown .ant-picker-super-next-icon {
  color: #1b524f !important;
  font-size: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.grafana-time-picker-dropdown .ant-picker-header button {
  color: #1b524f !important;
  border: none !important;
  background: transparent !important;
  padding: 6px 8px !important;
  border-radius: 4px !important;
  transition: all 0.2s ease !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.grafana-time-picker-dropdown .ant-picker-header button:hover {
  background-color: #e6f7ff !important;
  color: #1b524f !important;
}

.grafana-time-picker-dropdown .ant-picker-month-btn,
.grafana-time-picker-dropdown .ant-picker-year-btn {
  font-weight: 600 !important;
  font-size: 14px !important;
  color: #1b524f !important;
  padding: 6px 8px !important;
  border-radius: 4px !important;
  height: 32px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 !important;
  min-width: auto !important;
}

.grafana-time-picker-dropdown .ant-picker-month-btn:hover,
.grafana-time-picker-dropdown .ant-picker-year-btn:hover {
  background-color: #e6f7ff !important;
  color: #1b524f !important;
}

/* Make month/year selection panels more visible */
.grafana-time-picker-dropdown .ant-picker-month-panel,
.grafana-time-picker-dropdown .ant-picker-year-panel {
  background-color: #ffffff !important;
}

.grafana-time-picker-dropdown .ant-picker-cell {
  color: #000000 !important;
}

.grafana-time-picker-dropdown .ant-picker-cell:hover {
  background-color: #e6f7ff !important;
}

.grafana-time-picker-dropdown .ant-picker-cell-selected {
  background-color: #1b524f !important;
  color: #ffffff !important;
}

.grafana-time-picker-dropdown .ant-picker-cell-selected:hover {
  background-color: #164340 !important;
}

/* Enhanced navigation arrows for better visibility */
.grafana-time-picker-dropdown .ant-picker-header .ant-picker-header-prev-btn,
.grafana-time-picker-dropdown .ant-picker-header .ant-picker-header-next-btn,
.grafana-time-picker-dropdown .ant-picker-header .ant-picker-header-super-prev-btn,
.grafana-time-picker-dropdown .ant-picker-header .ant-picker-header-super-next-btn {
  width: 32px !important;
  height: 32px !important;
  line-height: 32px !important;
  border-radius: 4px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s ease !important;
  margin: 0 2px !important;
}

.grafana-time-picker-dropdown .ant-picker-header .ant-picker-header-prev-btn:hover,
.grafana-time-picker-dropdown .ant-picker-header .ant-picker-header-next-btn:hover,
.grafana-time-picker-dropdown .ant-picker-header .ant-picker-header-super-prev-btn:hover,
.grafana-time-picker-dropdown .ant-picker-header .ant-picker-header-super-next-btn:hover {
  background-color: #e6f7ff !important;
  border: 1px solid #1b524f !important;
}

/* Make the calendar dropdown more compact for 360px height */
.grafana-time-picker-dropdown .ant-picker-dropdown {
  max-height: none !important;
  z-index: 1060 !important;
}

.grafana-time-picker-dropdown .ant-picker-panel-container {
  max-height: none !important;
  overflow: visible !important;
}

/* Ensure DatePicker popup renders outside the dropdown container */
.ant-picker-dropdown {
  z-index: 1060 !important;
}

.ant-picker-panel-container {
  max-height: 400px !important;
  overflow-y: auto !important;
}

/* Animation for dropdown */
.grafana-time-picker-dropdown {
  animation: grafanaFadeIn 0.2s ease-out;
}

@keyframes grafanaFadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .grafana-time-picker-dropdown {
    width: 100vw;
    left: -12px !important;
    right: -12px !important;
    max-width: none;
    max-height: 350px;
  }

  .grafana-time-picker-button {
    width: 100% !important;
  }
}

/* Height adjustments for smaller screens */
@media (max-height: 800px) {
  .grafana-time-picker-dropdown {
    max-height: 350px;
  }
}

@media (max-height: 700px) {
  .grafana-time-picker-dropdown {
    max-height: 300px;
  }
}

@media (max-height: 600px) {
  .grafana-time-picker-dropdown {
    max-height: 250px;
  }
}

/* Options container responsive heights */
.grafana-options-container {
  max-height: 280px;
}

@media (max-height: 800px) {
  .grafana-options-container {
    max-height: 240px;
  }
}

@media (max-height: 700px) {
  .grafana-options-container {
    max-height: 200px;
  }
}

@media (max-height: 600px) {
  .grafana-options-container {
    max-height: 160px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .grafana-time-picker-button {
    border-width: 2px !important;
  }
  
  .grafana-time-picker-dropdown {
    border-width: 2px !important;
  }
  
  .grafana-option-item.selected {
    border-left-width: 3px !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .grafana-time-picker-button,
  .grafana-option-item,
  .grafana-search-input {
    transition: none !important;
  }
  
  .grafana-time-picker-dropdown {
    animation: none !important;
  }
}
