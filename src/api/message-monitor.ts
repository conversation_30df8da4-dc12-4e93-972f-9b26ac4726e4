import api from '../utils/api/baseApi';
import { BACKEND_URL_MESSAGE_MONITOR } from '@/utils';

// Types based on API documentation
export interface MessageMonitorItem {
  messageId: string;
  traceId?: string;
  producerId?: string; // maps to appCode in API
  topic?: string;
  sentStatus: number;
  retryNum?: number;
  isResent?: boolean;
  lastProcessedTime?: string;
  createdTime?: string;
  headers?: string;
  key?: string;
  payload?: string;
  trackingLogs?: MessageTracking[] | string;
  originMessageId?: string;
}

export interface MessageTracking {
  id: string;
  messageId: string;
  consumerId?: string;
  consumerService?: string;
  traceId?: string;
  code?: string;
  message?: string;
  errorTrace?: string;
  retryNum?: number;
  lastProcessedTime?: string;
  createdTime?: string;
}

export interface MessageMonitorSearchParams {
  messageId?: string;
  traceId?: string;
  appCode?: string;
  producerId?: string; // alias for appCode
  consumerId?: string;
  topic?: string;
  key?: string;
  code?: string;
  sentStatus?: number;
  isRelay?: boolean;
  fromDate?: string;
  toDate?: string;
  searchText?: string;
  createdTime?: string;
  createdTimeFrom?: string;
  createdTimeTo?: string;
  lastProcessedTime?: string;
  lastProcessedTimeFrom?: string;
  lastProcessedTimeTo?: string;
  page?: number;
  size?: number;
  sort?: string[];
}

export interface PaginatedResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
  numberOfElements: number;
  empty: boolean;
}

export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  code?: string;
  timestamp?: number;
  data?: T;
}

// Statistics types
export interface MessageSentStatistics {
  total: number;
  successful: number;
  pending: number;
  processing: number;
  failed: number;
}

export interface MessageReceivedStatistics {
  total: number;
  successful: number;
  failed: number;
}

export interface IntervalStatistics {
  label: string;
  messageCount: number;
  trackingCount: number;
}

export interface MessageStatisticsResponse {
  sentStats: MessageSentStatistics;
  receivedStats: MessageReceivedStatistics;
  intervals?: IntervalStatistics[];
}

export interface MessageStatisticsRequest {
  traceId?: string;
  appCode?: string;
  topic?: string;
  sentStatus?: string;
  fromDate?: string;
  toDate?: string;
  interval?: string; // "1m", "5m", "1h", "1d"
}

// Replay/Resend types
export interface BulkResendReplayRequest {
  type: 'RANGE' | 'IDS';
  producerId?: string | null; // maps to appCode in API
  description?: string | null;
  fromDate?: string | null;
  toDate?: string | null;
  messageIds?: string[];
  scheduleAt?: string | null;
}

export interface ReplayRequest {
  id: string;
  type: 'RANGE' | 'IDS';
  appCode: string;
  fromDate?: string;
  toDate?: string;
  messageIds?: string[];
  status: 'SCHEDULED' | 'RUNNING' | 'COMPLETED' | 'FAILED';
  description?: string;
  totalMessages?: number;
  processedMessages?: number;
  updatedTime?: string;
  createdTime?: string;
  createdBy?: string;
}

export interface SearchReplayRequest {
  type?: 'RANGE' | 'IDS';
  appCode?: string;
  status?: 'SCHEDULED' | 'RUNNING' | 'COMPLETED' | 'FAILED';
  fromDate?: string;
  toDate?: string;
  page?: number;
  size?: number;
  sort?: string[];
}

// API Class
export class MessageMonitorApi {
  // Get all messages with search and pagination
  static async getAll(params: MessageMonitorSearchParams = {}): Promise<ApiResponse<PaginatedResponse<MessageMonitorItem>>> {
    try {
      const url = `${BACKEND_URL_MESSAGE_MONITOR}`;

      // Transform params to match API expectations
      const apiParams: any = {
        page: params.page || 0,
        size: params.size || 10,
      };

      // Map frontend params to API params
      if (params.messageId) apiParams.messageId = params.messageId;
      if (params.traceId) apiParams.traceId = params.traceId;
      if (params.producerId) apiParams.appCode = params.producerId; // Map producerId to appCode
      if (params.appCode) apiParams.appCode = params.appCode;
      if (params.consumerId) apiParams.consumerId = params.consumerId;
      if (params.topic) apiParams.topic = params.topic;
      if (params.key) apiParams.key = params.key;
      if (params.code) apiParams.code = params.code;
      if (params.sentStatus !== undefined) apiParams.sentStatus = params.sentStatus;
      if (params.isRelay !== undefined) apiParams.isRelay = params.isRelay;

      // Handle date parameters
      if (params.fromDate) apiParams.fromDate = params.fromDate;
      if (params.toDate) apiParams.toDate = params.toDate;
      if (params.createdTime) apiParams.fromDate = params.createdTime;
      if (params.createdTimeFrom) apiParams.fromDate = params.createdTimeFrom;
      if (params.createdTimeTo) apiParams.toDate = params.createdTimeTo;
      if (params.lastProcessedTime) apiParams.toDate = params.lastProcessedTime;
      if (params.lastProcessedTimeFrom) apiParams.fromDate = params.lastProcessedTimeFrom;
      if (params.lastProcessedTimeTo) apiParams.toDate = params.lastProcessedTimeTo;

      // Handle search text - could map to multiple fields
      if (params.searchText) {
        // For now, map to messageId or traceId search
        apiParams.messageId = params.searchText;
      }

      if (params.sort) apiParams.sort = params.sort;

      const response = await api.get<ApiResponse<PaginatedResponse<MessageMonitorItem>>>(url, { params: apiParams });

      // Transform API response to match frontend expectations
      const transformedData: PaginatedResponse<MessageMonitorItem> = {
        content: (response.data.data?.content || []).map((item: any) => ({
          messageId: item.messageId,
          traceId: item.traceId,
          producerId: item.appCode, // Map appCode back to producerId
          topic: item.topic,
          sentStatus: item.sentStatus === "0" ? 0 : item.sentStatus === "1" ? 1 : item.sentStatus === "2" ? 2 : item.sentStatus === "-1" ? -1 : item.sentStatus,
          retryNum: item.retryNum,
          isResent: item.isResent,
          lastProcessedTime: item.lastProcessedTime,
          createdTime: item.createdTime,
          headers: item.headers,
          key: item.key,
          payload: item.payload,
          trackingLogs: item.trackingLogs,
          originMessageId: item.originMessageId,
        })),
        totalElements: response.data.data?.totalElements || 0,
        totalPages: response.data.data?.totalPages || 0,
        size: response.data.data?.size || 10,
        number: response.data.data?.number || 0,
        first: response.data.data?.first || true,
        last: response.data.data?.last || true,
        numberOfElements: response.data.data?.numberOfElements || 0,
        empty: response.data.data?.empty || false,
      };

      // Return transformed data in baseApi format
      return {
        ...response.data,
        data: transformedData
      };
    } catch (error) {
      throw error;
    }
  }

  // Get message by ID
  static async getById(messageId: string): Promise<ApiResponse<MessageMonitorItem | null>> {
    try {
      const url = `${BACKEND_URL_MESSAGE_MONITOR}/${messageId}`;

      const response = await api.get<any>(url);

      // Transform API response to match frontend expectations
      const item = response.data.data;
      const transformedData: MessageMonitorItem | null = item ? {
        messageId: item.messageId,
        traceId: item.traceId,
        producerId: item.appCode, // Map appCode back to producerId
        topic: item.topic,
        sentStatus: typeof item.sentStatus === 'string' ? (
          item.sentStatus === "0" ? 0 : 
          item.sentStatus === "1" ? 1 : 
          item.sentStatus === "2" ? 2 : 
          item.sentStatus === "-1" ? -1 : 
          parseInt(item.sentStatus) || 0
        ) : item.sentStatus,
        retryNum: item.retryNum,
        isResent: item.isResent,
        lastProcessedTime: item.lastProcessedTime,
        createdTime: item.createdTime,
        headers: item.headers,
        key: item.key,
        payload: item.payload,
        trackingLogs: item.trackingLogs,
        originMessageId: item.originMessageId,
      } : null;

      return {
        ...response.data,
        data: transformedData
      };
    } catch (error) {
      throw error;
    }
  }

  // Get message tracking by traceId
  static async getTrackingByTraceId(traceId: string): Promise<ApiResponse<MessageTracking[]>> {
    try {
      const url = `${BACKEND_URL_MESSAGE_MONITOR}/tracking/${traceId}`;

      const response = await api.get<ApiResponse<MessageTracking[]>>(url);

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Get message statistics
  static async getStatistics(params: MessageStatisticsRequest): Promise<ApiResponse<MessageStatisticsResponse>> {
    try {
      const url = `${BACKEND_URL_MESSAGE_MONITOR}/statistics`;

      const response = await api.get<ApiResponse<MessageStatisticsResponse>>(url, { params });

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Bulk resend/replay messages
  static async bulkResendReplay(params: BulkResendReplayRequest): Promise<ApiResponse<void>> {
    try {
      const url = `${BACKEND_URL_MESSAGE_MONITOR}/replay`;

      // Transform frontend params to API format
      const apiRequest = {
        type: params.type,
        appCode: params.producerId, // Map producerId to appCode
        description: params.description,
        fromDate: params.fromDate,
        toDate: params.toDate,
        messageIds: params.messageIds,
        scheduleAt: params.scheduleAt,
      };

      const response = await api.post<ApiResponse<void>>(url, apiRequest);

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Search replay requests
  static async searchReplayRequests(params: SearchReplayRequest = {}): Promise<ApiResponse<PaginatedResponse<ReplayRequest>>> {
    try {
      const url = `${BACKEND_URL_MESSAGE_MONITOR}/replay`;

      const apiParams = {
        ...params,
        page: params.page || 0,
        size: params.size || 10,
      };

      const response = await api.get<ApiResponse<PaginatedResponse<ReplayRequest>>>(url, { params: apiParams });

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Get replay request by ID
  static async getReplayRequestById(id: string): Promise<ApiResponse<ReplayRequest>> {
    try {
      const url = `${BACKEND_URL_MESSAGE_MONITOR}/replay/${id}`;

      const response = await api.get<ApiResponse<ReplayRequest>>(url);

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Single message resend using new endpoint POST /message/{messageId}/replay
  static async resendMessage(messageId: string, customHeaders?: string, customPayload?: string, customKey?: string): Promise<ApiResponse<void>> {
    try {
      // Get current message details to get original values if custom values not provided
      const messageResponse = await this.getById(messageId);

      if (!messageResponse.success || !messageResponse.data) {
        throw new Error('Cannot get message details for resend');
      }

      const record = messageResponse.data;

      // Use custom values if provided, otherwise use original values
      const finalHeaders = customHeaders !== undefined ? customHeaders : (record.headers || '');
      const finalPayload = customPayload !== undefined ? customPayload : (record.payload || '');
      const finalKey = customKey !== undefined ? customKey : (record.key || '');

      const apiRequest: any = {
        headers: finalHeaders,
        payload: finalPayload,
        key: finalKey
      };

      const url = `${BACKEND_URL_MESSAGE_MONITOR}/${messageId}/replay`;
      const response = await api.post<ApiResponse<void>>(url, apiRequest);

      return response.data;
    } catch (error) {
      throw error;
    }
  }
}
